<?php
/**
 * Test script to verify timezone update functionality
 */

// Include the application bootstrap
require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/UserModel.php';

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Timezone Update Test</h1>";

// Create database connection
$db = new Database();

// Test user ID (you'll need to replace this with a valid user ID)
$testUserId = 1; // Change this to a valid user ID

echo "<h2>1. Current User Data</h2>";

// Get current user data
$db->query('SELECT id, name, email, timezone FROM users WHERE id = :id');
$db->bind(':id', $testUserId);
$currentUser = $db->single();

if ($currentUser) {
    echo "<p><strong>User ID:</strong> {$currentUser->id}</p>";
    echo "<p><strong>Name:</strong> {$currentUser->name}</p>";
    echo "<p><strong>Email:</strong> {$currentUser->email}</p>";
    echo "<p><strong>Current Timezone:</strong> " . ($currentUser->timezone ?? 'NULL') . "</p>";
} else {
    echo "<p style='color: red;'>User not found with ID: $testUserId</p>";
    exit;
}

echo "<h2>2. Testing Timezone Update</h2>";

// Create UserModel instance
$userModel = new UserModel();

// Test data for update
$testData = [
    'id' => $testUserId,
    'name' => $currentUser->name,
    'phone' => null,
    'address' => null,
    'city' => null,
    'state' => null,
    'zip' => null,
    'timezone' => 'America/Los_Angeles', // Test timezone
    'password' => '' // No password change
];

echo "<p><strong>Test timezone value:</strong> {$testData['timezone']}</p>";

// Perform the update
$updateResult = $userModel->updateProfile($testData);

echo "<p><strong>Update result:</strong> " . ($updateResult ? 'SUCCESS' : 'FAILED') . "</p>";

echo "<h2>3. Verify Update</h2>";

// Get updated user data
$db->query('SELECT id, name, email, timezone FROM users WHERE id = :id');
$db->bind(':id', $testUserId);
$updatedUser = $db->single();

if ($updatedUser) {
    echo "<p><strong>Updated Timezone:</strong> " . ($updatedUser->timezone ?? 'NULL') . "</p>";
    
    if ($updatedUser->timezone === $testData['timezone']) {
        echo "<p style='color: green;'><strong>✓ SUCCESS:</strong> Timezone was updated correctly!</p>";
    } else {
        echo "<p style='color: red;'><strong>✗ FAILED:</strong> Timezone was not updated correctly.</p>";
        echo "<p>Expected: {$testData['timezone']}</p>";
        echo "<p>Actual: " . ($updatedUser->timezone ?? 'NULL') . "</p>";
    }
} else {
    echo "<p style='color: red;'>Could not retrieve updated user data.</p>";
}

echo "<h2>4. Restore Original Timezone</h2>";

// Restore original timezone
$restoreData = [
    'id' => $testUserId,
    'name' => $currentUser->name,
    'phone' => null,
    'address' => null,
    'city' => null,
    'state' => null,
    'zip' => null,
    'timezone' => $currentUser->timezone,
    'password' => ''
];

$restoreResult = $userModel->updateProfile($restoreData);
echo "<p><strong>Restore result:</strong> " . ($restoreResult ? 'SUCCESS' : 'FAILED') . "</p>";

// Verify restoration
$db->query('SELECT timezone FROM users WHERE id = :id');
$db->bind(':id', $testUserId);
$restoredUser = $db->single();

if ($restoredUser && $restoredUser->timezone === $currentUser->timezone) {
    echo "<p style='color: green;'><strong>✓ SUCCESS:</strong> Original timezone restored!</p>";
} else {
    echo "<p style='color: orange;'><strong>⚠ WARNING:</strong> Could not restore original timezone.</p>";
}

echo "<h2>5. Check Error Logs</h2>";
echo "<p>Check your error logs for debug messages starting with 'UserModel::updateProfile' and 'UserController::profile'</p>";
echo "<p>If DEBUG_MODE is enabled, you should see detailed logging of the update process.</p>";

?>
