<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>My Profile</h1>
        </div>
        <div class="col-md-6 text-end">
            <a href="<?php echo BASE_URL; ?>/user/dashboard" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Edit Profile</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="d-flex align-items-center">
                                <div class="rounded-circle overflow-hidden me-3" style="width: 100px; height: 100px; background-color: #f8f9fa; display: flex; align-items: center; justify-content: center;">
                                    <?php 
                                    // Use our new helper function to display the profile image
                                    $imageUrl = getUserProfileImageUrl($user->id);
                                    if ($imageUrl) : 
                                    ?>
                                        <img src="<?php echo htmlspecialchars($imageUrl); ?>" alt="Profile" class="img-fluid" style="object-fit: cover; width: 100%; height: 100%;">
                                    <?php else : ?>
                                        <i class="fas fa-user-circle text-secondary" style="font-size: 5rem;"></i>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <h4 class="mb-1"><?php echo $user->name; ?></h4>
                                    <p class="text-muted mb-0"><?php echo $user->email; ?></p>
                                    <p class="mb-0">
                                        Role: 
                                        <?php if ($user->role == 'admin') : ?>
                                            <span class="badge bg-danger">Administrator</span>
                                        <?php elseif ($user->role == 'coordinator') : ?>
                                            <span class="badge bg-primary">Show Coordinator</span>
                                        <?php elseif ($user->role == 'judge') : ?>
                                            <span class="badge bg-success">Judge</span>
                                        <?php else : ?>
                                            <span class="badge bg-info">Registered User</span>
                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form action="<?php echo BASE_URL; ?>/user/profile" method="post">
                        <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">Name</label>
                            <input type="text" class="form-control <?php echo (!empty($name_err)) ? 'is-invalid' : ''; ?>" 
                                id="name" name="name" value="<?php echo $name; ?>" required>
                            <div class="invalid-feedback"><?php echo $name_err; ?></div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" value="<?php echo $user->email; ?>" disabled>
                            <div class="form-text">Email cannot be changed. Contact administrator for assistance.</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="phone" name="phone" value="<?php echo $phone; ?>">
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <input type="text" class="form-control" id="address" name="address" value="<?php echo $address; ?>">
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="city" class="form-label">City</label>
                                <input type="text" class="form-control" id="city" name="city" value="<?php echo $city; ?>">
                            </div>

                            <div class="col-md-3 mb-3">
                                <label for="state" class="form-label">State</label>
                                <input type="text" class="form-control" id="state" name="state" value="<?php echo $state; ?>">
                            </div>

                            <div class="col-md-3 mb-3">
                                <label for="zip" class="form-label">ZIP Code</label>
                                <input type="text" class="form-control" id="zip" name="zip" value="<?php echo $zip; ?>">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="timezone" class="form-label">
                                <i class="fas fa-clock me-2"></i>Timezone
                            </label>
                            <select class="form-control <?php echo (!empty($timezone_err)) ? 'is-invalid' : ''; ?>" id="timezone" name="timezone">
                                <option value="">Select your timezone...</option>
                                <?php
                                // Use the timezone helper function
                                $usa_timezones = getUSATimezones();
                                $current_timezone = $timezone; // Use the timezone from the data array

                                foreach ($usa_timezones as $tz_value => $tz_label) {
                                    $selected = ($current_timezone == $tz_value) ? 'selected' : '';
                                    $abbr = getTimezoneAbbreviation($tz_value);
                                    $display_label = $tz_label . ($abbr ? " ({$abbr})" : '');
                                    echo "<option value=\"{$tz_value}\" {$selected}>{$display_label}</option>";
                                }
                                ?>
                            </select>
                            <div class="invalid-feedback"><?php echo $timezone_err; ?></div>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Select your local timezone to see event times displayed correctly.
                                <?php if (isset($user->timezone) && !empty($user->timezone)): ?>
                                    <br><small class="text-muted">
                                        Current time in your timezone:
                                        <strong><?php echo formatDateTimeForUser(gmdate('Y-m-d H:i:s'), $user->id, 'M j, Y g:i A T'); ?></strong>
                                    </small>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">New Password</label>
                            <input type="password" class="form-control <?php echo (!empty($password_err)) ? 'is-invalid' : ''; ?>" 
                                id="password" name="password">
                            <div class="invalid-feedback"><?php echo $password_err; ?></div>
                            <div class="form-text">Leave blank to keep current password. New password must be at least 6 characters long.</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirm New Password</label>
                            <input type="password" class="form-control <?php echo (!empty($confirm_password_err)) ? 'is-invalid' : ''; ?>" 
                                id="confirm_password" name="confirm_password">
                            <div class="invalid-feedback"><?php echo $confirm_password_err; ?></div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i> Update Profile
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">Profile Image</h5>
                </div>
                <div class="card-body">
                    <?php
                    // Get user's profile image
                    $db = new Database();
                    $db->query('SELECT * FROM images WHERE entity_type = :entity_type AND entity_id = :entity_id AND is_primary = 1');
                    $db->bind(':entity_type', 'user');
                    $db->bind(':entity_id', $user->id);
                    $profileImage = $db->single();
                    ?>
                    
                    <?php 
                    // Get the profile image URL using our helper function
                    $profileImageUrl = getUserProfileImageUrl($user->id);
                    if ($profileImageUrl) : 
                    ?>
                        <div class="text-center mb-4">
                            <img src="<?php echo htmlspecialchars($profileImageUrl); ?>" alt="Profile" class="img-fluid rounded-circle mb-3" style="max-width: 200px; max-height: 200px;">
                            <div class="d-flex justify-content-center gap-2 mt-2">
                                <form action="<?php echo BASE_URL; ?>/user/deleteProfileImage" method="post">
                                    <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                                    <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete your profile image?');">
                                        <i class="fas fa-trash me-2"></i> Delete Image
                                    </button>
                                </form>
                                
                                <?php 
                                // Use the facebook_id from the user object directly
                                if (!empty($user->facebook_id)) : 
                                ?>
                                <form action="<?php echo BASE_URL; ?>/user/syncFacebookImage" method="post">
                                    <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fab fa-facebook me-2"></i> Sync Facebook Image
                                    </button>
                                </form>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php 
                        // Check if we have a profile image in the database (not just a URL)
                        if (isset($profileImage) && $profileImage && isset($profileImage->id)) : 
                        ?>
                        <div class="text-center mb-3">
                            <a href="<?php echo BASE_URL; ?>/image_editor/edit/<?php echo $profileImage->id; ?>" class="btn btn-outline-primary">
                                <i class="fas fa-edit me-2"></i> Edit Image
                            </a>
                        </div>
                        <?php endif; ?>
                        <hr>
                    <?php endif; ?>
                    
                    <form action="<?php echo BASE_URL; ?>/user/updateProfileImage" method="post" enctype="multipart/form-data">
                        <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                        
                        <div class="mb-3">
                            <label for="profile_image" class="form-label">Upload Profile Image</label>
                            <input type="file" class="form-control" id="profile_image" name="profile_image" accept="image/*">
                            <div class="form-text">Allowed file types: JPG, JPEG, PNG, GIF. Maximum file size: 5MB.</div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-info">
                                <i class="fas fa-upload me-2"></i> Upload Image
                            </button>
                        </div>
                    </form>
                    
                    <!-- Facebook sync functionality is now handled automatically by the image helper -->
                    
                    <div class="mt-3 text-center">
                        <a href="<?php echo BASE_URL; ?>/image_editor/upload/user/<?php echo $user->id; ?>" class="btn btn-outline-success">
                            <i class="fas fa-cloud-upload-alt me-2"></i> Use Advanced Image Uploader
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Notification Preferences Section -->
    <div class="row mt-4">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bell me-2"></i> Notification Preferences
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-4">
                        Manage how you receive notifications about events, car shows, and other important updates.
                    </p>
                    
                    <div class="text-center">
                        <div class="btn-group" role="group">
                            <a href="<?php echo BASE_URL; ?>/user/notifications" class="btn btn-warning">
                                <i class="fas fa-cog me-2"></i>Notification Settings
                            </a>
                            <a href="<?php echo BASE_URL; ?>/user/event_subscriptions" class="btn btn-outline-warning">
                                <i class="fas fa-calendar-check me-2"></i>Event Subscriptions
                            </a>
                        </div>
                    </div>
                    
                    <hr class="my-4">
                    
                    <div class="row text-center">
                        <div class="col-md-3 mb-3">
                            <div class="p-3 border rounded">
                                <i class="fas fa-envelope text-primary fa-2x mb-2"></i>
                                <h6>Email</h6>
                                <small class="text-muted">Event reminders via email</small>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="p-3 border rounded">
                                <i class="fas fa-sms text-success fa-2x mb-2"></i>
                                <h6>SMS</h6>
                                <small class="text-muted">Text message alerts</small>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="p-3 border rounded">
                                <i class="fas fa-desktop text-info fa-2x mb-2"></i>
                                <h6>Push</h6>
                                <small class="text-muted">Browser notifications</small>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="p-3 border rounded">
                                <i class="fas fa-bell text-warning fa-2x mb-2"></i>
                                <h6>Toast</h6>
                                <small class="text-muted">In-app notifications</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>